2025-06-22T10:12:33.070471+0200 - INFO - Starting download: https://www.instagram.com/reel/DGv6G6bzH0s/?igsh=bHNiam9wZzBlcGd0 - {}
2025-06-22T10:12:33.280101+0200 - DEBUG - [Instagram] Extracting URL: https://www.instagram.com/reel/DGv6G6bzH0s/?igsh=bHNiam9wZzBlcGd0 - {}
2025-06-22T10:12:33.284864+0200 - DEBUG - [Instagram] DGv6G6bzH0s: Setting up session - {}
2025-06-22T10:12:33.735769+0200 - WARNING - [Instagram] DGv6G6bzH0s: No csrf token set by Instagram API - {}
2025-06-22T10:12:33.738511+0200 - DEBUG - [Instagram] DGv6G6bzH0s: Downloading JSON metadata - {}
2025-06-22T10:12:34.185060+0200 - ERROR - ERROR: [Instagram] DGv6G6bzH0s: Instagram sent an empty media response. Check if this post is accessible in your browser without being logged-in. If it is not, then use --cookies-from-browser or --cookies for the authentication. See  https://github.com/yt-dlp/yt-dlp/wiki/FAQ#how-do-i-pass-cookies-to-yt-dlp  for how to manually pass cookies. Otherwise, if the post is accessible in browser without being logged-in, please report this issue on  https://github.com/yt-dlp/yt-dlp/issues?q= , filling out the appropriate issue template. Confirm you are on the latest version using  yt-dlp -U - {}
2025-06-22T10:12:34.188729+0200 - ERROR - Error downloading https://www.instagram.com/reel/DGv6G6bzH0s/?igsh=bHNiam9wZzBlcGd0: ERROR: [Instagram] DGv6G6bzH0s: Instagram sent an empty media response. Check if this post is accessible in your browser without being logged-in. If it is not, then use --cookies-from-browser or --cookies for the authentication. See  https://github.com/yt-dlp/yt-dlp/wiki/FAQ#how-do-i-pass-cookies-to-yt-dlp  for how to manually pass cookies. Otherwise, if the post is accessible in browser without being logged-in, please report this issue on  https://github.com/yt-dlp/yt-dlp/issues?q= , filling out the appropriate issue template. Confirm you are on the latest version using  yt-dlp -U - {}
Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\__SCRATCH__\my_downloaders\py__InstagramDownloader\venv\Lib\site-packages\yt_dlp\YoutubeDL.py", line 1662, in wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {}
           │    │      └ ('https://www.instagram.com/reel/DGv6G6bzH0s/?igsh=bHNiam9wZzBlcGd0', <yt_dlp.extractor.instagram.InstagramIE object at 0x000...
           │    └ <yt_dlp.YoutubeDL.YoutubeDL object at 0x000001C07E5F3E00>
           └ <function YoutubeDL.__extract_info at 0x000001C07E67D8A0>
  File "C:\Users\<USER>\Desktop\__SCRATCH__\my_downloaders\py__InstagramDownloader\venv\Lib\site-packages\yt_dlp\YoutubeDL.py", line 1797, in __extract_info
    ie_result = ie.extract(url)
                │  │       └ 'https://www.instagram.com/reel/DGv6G6bzH0s/?igsh=bHNiam9wZzBlcGd0'
                │  └ <function InfoExtractor.extract at 0x000001C07E500CC0>
                └ <yt_dlp.extractor.instagram.InstagramIE object at 0x000001C07E724D70>
  File "C:\Users\<USER>\Desktop\__SCRATCH__\my_downloaders\py__InstagramDownloader\venv\Lib\site-packages\yt_dlp\extractor\common.py", line 748, in extract
    ie_result = self._real_extract(url)
                │    │             └ 'https://www.instagram.com/reel/DGv6G6bzH0s/?igsh=bHNiam9wZzBlcGd0'
                │    └ <function InstagramIE._real_extract at 0x000001C07EB63F60>
                └ <yt_dlp.extractor.instagram.InstagramIE object at 0x000001C07E724D70>
  File "C:\Users\<USER>\Desktop\__SCRATCH__\my_downloaders\py__InstagramDownloader\venv\Lib\site-packages\yt_dlp\extractor\instagram.py", line 448, in _real_extract
    raise ExtractorError(
          └ <class 'yt_dlp.utils.ExtractorError'>

yt_dlp.utils.ExtractorError: [Instagram] DGv6G6bzH0s: Instagram sent an empty media response. Check if this post is accessible in your browser without being logged-in. If it is not, then use --cookies-from-browser or --cookies for the authentication. See  https://github.com/yt-dlp/yt-dlp/wiki/FAQ#how-do-i-pass-cookies-to-yt-dlp  for how to manually pass cookies. Otherwise, if the post is accessible in browser without being logged-in, please report this issue on  https://github.com/yt-dlp/yt-dlp/issues?q= , filling out the appropriate issue template. Confirm you are on the latest version using  yt-dlp -U


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\Python313\Lib\threading.py", line 1014, in _bootstrap
    self._bootstrap_inner()
    │    └ <function Thread._bootstrap_inner at 0x000001C07CD5DA80>
    └ <Thread(ThreadPoolExecutor-0_0, started 26544)>
  File "C:\Python313\Lib\threading.py", line 1043, in _bootstrap_inner
    self.run()
    │    └ <function Thread.run at 0x000001C07CD5D800>
    └ <Thread(ThreadPoolExecutor-0_0, started 26544)>
  File "C:\Python313\Lib\threading.py", line 994, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {}
    │    │        │    │        └ <Thread(ThreadPoolExecutor-0_0, started 26544)>
    │    │        │    └ (<weakref at 0x000001C07E74CE50; to 'concurrent.futures.thread.ThreadPoolExecutor' at 0x000001C07E726270>, <_queue.SimpleQueu...
    │    │        └ <Thread(ThreadPoolExecutor-0_0, started 26544)>
    │    └ <function _worker at 0x000001C07D60D3A0>
    └ <Thread(ThreadPoolExecutor-0_0, started 26544)>
  File "C:\Python313\Lib\concurrent\futures\thread.py", line 93, in _worker
    work_item.run()
    │         └ <function _WorkItem.run at 0x000001C07D60D4E0>
    └ <concurrent.futures.thread._WorkItem object at 0x000001C07E726510>
  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │    │   │    │       │    └ {}
             │    │   │    │       └ <concurrent.futures.thread._WorkItem object at 0x000001C07E726510>
             │    │   │    └ ('https://www.instagram.com/reel/DGv6G6bzH0s/?igsh=bHNiam9wZzBlcGd0', WindowsPath('C:/Users/<USER>/Desktop/__SCRATCH__/my_downlo...
             │    │   └ <concurrent.futures.thread._WorkItem object at 0x000001C07E726510>
             │    └ <function download_video at 0x000001C07E730FE0>
             └ <concurrent.futures.thread._WorkItem object at 0x000001C07E726510>

> File "C:\Users\<USER>\Desktop\__SCRATCH__\my_downloaders\py__InstagramDownloader\src\main.py", line 147, in download_video
    ydl.download([link])
    │   │         └ 'https://www.instagram.com/reel/DGv6G6bzH0s/?igsh=bHNiam9wZzBlcGd0'
    │   └ <function YoutubeDL.download at 0x000001C07E67EB60>
    └ <yt_dlp.YoutubeDL.YoutubeDL object at 0x000001C07E5F3E00>

  File "C:\Users\<USER>\Desktop\__SCRATCH__\my_downloaders\py__InstagramDownloader\venv\Lib\site-packages\yt_dlp\YoutubeDL.py", line 3643, in download
    self.__download_wrapper(self.extract_info)(
    │                       │    └ <function YoutubeDL.extract_info at 0x000001C07E67D580>
    │                       └ <yt_dlp.YoutubeDL.YoutubeDL object at 0x000001C07E5F3E00>
    └ <yt_dlp.YoutubeDL.YoutubeDL object at 0x000001C07E5F3E00>
  File "C:\Users\<USER>\Desktop\__SCRATCH__\my_downloaders\py__InstagramDownloader\venv\Lib\site-packages\yt_dlp\YoutubeDL.py", line 3616, in wrapper
    res = func(*args, **kwargs)
          │     │       └ {'force_generic_extractor': False}
          │     └ ('https://www.instagram.com/reel/DGv6G6bzH0s/?igsh=bHNiam9wZzBlcGd0',)
          └ <bound method YoutubeDL.extract_info of <yt_dlp.YoutubeDL.YoutubeDL object at 0x000001C07E5F3E00>>
  File "C:\Users\<USER>\Desktop\__SCRATCH__\my_downloaders\py__InstagramDownloader\venv\Lib\site-packages\yt_dlp\YoutubeDL.py", line 1651, in extract_info
    return self.__extract_info(url, self.get_info_extractor(key), download, extra_info, process)
           │                   │    │    │                  │     │         │           └ True
           │                   │    │    │                  │     │         └ {}
           │                   │    │    │                  │     └ True
           │                   │    │    │                  └ 'Instagram'
           │                   │    │    └ <function YoutubeDL.get_info_extractor at 0x000001C07E5FB7E0>
           │                   │    └ <yt_dlp.YoutubeDL.YoutubeDL object at 0x000001C07E5F3E00>
           │                   └ 'https://www.instagram.com/reel/DGv6G6bzH0s/?igsh=bHNiam9wZzBlcGd0'
           └ <yt_dlp.YoutubeDL.YoutubeDL object at 0x000001C07E5F3E00>
  File "C:\Users\<USER>\Desktop\__SCRATCH__\my_downloaders\py__InstagramDownloader\venv\Lib\site-packages\yt_dlp\YoutubeDL.py", line 1680, in wrapper
    self.report_error(str(e), e.format_traceback())
    │    └ <function YoutubeDL.report_error at 0x000001C07E67C9A0>
    └ <yt_dlp.YoutubeDL.YoutubeDL object at 0x000001C07E5F3E00>
  File "C:\Users\<USER>\Desktop\__SCRATCH__\my_downloaders\py__InstagramDownloader\venv\Lib\site-packages\yt_dlp\YoutubeDL.py", line 1120, in report_error
    self.trouble(f'{self._format_err("ERROR:", self.Styles.ERROR)} {message}', *args, **kwargs)
    │    │          │    │                     │    │      │        │           │       └ {}
    │    │          │    │                     │    │      │        │           └ ('  File "C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\my_downloaders\\py__InstagramDownloader\\venv\\Lib\\site-packages\\yt_dlp\\ex...
    │    │          │    │                     │    │      │        └ '[Instagram] DGv6G6bzH0s: Instagram sent an empty media response. Check if this post is accessible in your browser without be...
    │    │          │    │                     │    │      └ 'red'
    │    │          │    │                     │    └ Namespace(HEADERS='yellow', EMPHASIS='light blue', FILENAME='green', ID='green', DELIM='blue', ERROR='red', BAD_FORMAT='light...
    │    │          │    │                     └ <yt_dlp.YoutubeDL.YoutubeDL object at 0x000001C07E5F3E00>
    │    │          │    └ <function YoutubeDL._format_err at 0x000001C07E67C720>
    │    │          └ <yt_dlp.YoutubeDL.YoutubeDL object at 0x000001C07E5F3E00>
    │    └ <function YoutubeDL.trouble at 0x000001C07E67C4A0>
    └ <yt_dlp.YoutubeDL.YoutubeDL object at 0x000001C07E5F3E00>
  File "C:\Users\<USER>\Desktop\__SCRATCH__\my_downloaders\py__InstagramDownloader\venv\Lib\site-packages\yt_dlp\YoutubeDL.py", line 1059, in trouble
    raise DownloadError(message, exc_info)
          │             │        └ (<class 'yt_dlp.utils.ExtractorError'>, ExtractorError('[Instagram] DGv6G6bzH0s: Instagram sent an empty media response. Chec...
          │             └ 'ERROR: [Instagram] DGv6G6bzH0s: Instagram sent an empty media response. Check if this post is accessible in your browser wit...
          └ <class 'yt_dlp.utils.DownloadError'>

yt_dlp.utils.DownloadError: ERROR: [Instagram] DGv6G6bzH0s: Instagram sent an empty media response. Check if this post is accessible in your browser without being logged-in. If it is not, then use --cookies-from-browser or --cookies for the authentication. See  https://github.com/yt-dlp/yt-dlp/wiki/FAQ#how-do-i-pass-cookies-to-yt-dlp  for how to manually pass cookies. Otherwise, if the post is accessible in browser without being logged-in, please report this issue on  https://github.com/yt-dlp/yt-dlp/issues?q= , filling out the appropriate issue template. Confirm you are on the latest version using  yt-dlp -U
