import webbrowser
import sys
from rich.console import Console

console = Console()

DOWNLOAD_SITES = [
    ("SaveFrom.net", "https://savefrom.net/"),
    ("DownloadGram", "https://downloadgram.com/"),
    ("InstaDownloader", "https://instadownloader.co/"),
    ("SnapInsta", "https://snapinsta.app/"),
    ("InstaSave", "https://instasave.website/")
]

def open_alternatives():
    console.print("[bold cyan]Opening Instagram download alternatives...[/bold cyan]\n")

    for name, url in DOWNLOAD_SITES:
        try:
            console.print(f"[green]✓[/green] Opening {name}...")
            webbrowser.open(url)
        except Exception as e:
            console.print(f"[red]✗[/red] Failed to open {name}: {e}")

    console.print(f"\n[bold green]Opened {len(DOWNLOAD_SITES)} alternative sites![/bold green]")
    console.print("\n[bold yellow]Instructions:[/bold yellow]")
    console.print("1. Copy your Instagram URL")
    console.print("2. Paste it into any opened website")
    console.print("3. Click download")

    console.print("\n[dim]Browser-based services are more reliable than automated tools[/dim]")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        console.print("[bold]Instagram Download Alternatives[/bold]")
        console.print("Opens browser-based Instagram download services")
        console.print("Usage: python open_alternatives.py")
    else:
        open_alternatives()
        console.print("\n[bold cyan]Press Enter to exit...[/bold cyan]")
        input()
