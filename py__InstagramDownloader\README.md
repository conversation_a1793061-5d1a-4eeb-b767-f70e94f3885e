# Instagram Downloader

Download Instagram content using instaloader with automatic fallback to browser alternatives when blocked.

## Setup

1. Run `.\py_venv_init.bat` to initialize environment
2. Run `.\main.bat` to start the application

## Usage

The application will:
- Check clipboard for Instagram URLs
- Prompt for URLs and output directory
- Attempt download with progress tracking
- Show browser alternatives if Instagram blocks the request

## Download Options

1. Video + Images: Full Quality
2. Video Only: Full Quality
3. Images Only: Full Quality
4. Video + Images: Compressed

## Note

Instagram actively blocks automated downloading tools. This tool provides immediate browser-based alternatives when blocked.
