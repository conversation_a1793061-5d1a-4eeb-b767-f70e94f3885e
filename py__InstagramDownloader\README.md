# Instagram Downloader

Download Instagram content with automatic fallback to browser-based alternatives when Instagram blocks automated tools.

## ⚠️ Important Notice

Instagram actively blocks automated downloading tools. This tool provides immediate alternatives when blocked.

## Features

- Download Instagram reels, posts, and IGTV content
- Multiple download options (videos, images, or both)
- Automatic clipboard URL detection
- Rich console interface with progress tracking
- Automatic alternative suggestions when blocked

## Supported URLs

The downloader supports Instagram URLs in the following formats:
- `https://www.instagram.com/reel/DGv6G6bzH0s/`
- `https://instagram.com/p/ABC123def/`
- `https://www.instagram.com/tv/XYZ789/`
- URLs with query parameters (e.g., `?igsh=...`)

## Setup

1. **Initialize Environment**: `.\py_venv_init.bat`
2. **Run Application**: `.\main.bat`

## Usage

### Quick Alternative Access (Recommended)
```cmd
.\open_alternatives.bat
```
Opens reliable browser-based download services.

### Automated Downloader (Experimental)
```cmd
.\main.bat
```
Attempts automated download, shows alternatives if blocked.

### Command Line
```cmd
python main.py -i "URL" -op "C:\Downloads"
```

## Download Options

1. **Video + Images: Full Quality** - Both with full metadata
2. **Video Only: Full Quality** - Videos with compressed metadata
3. **Images Only: Full Quality** - Images with compressed metadata
4. **Video + Images: Compressed** - Both with minimal metadata

## Dependencies

- `instaloader` - Instagram downloading
- `rich` - Console interface
- `loguru` - Logging
- `pyperclip` - Clipboard integration

## Troubleshooting

**Download failures**: Instagram blocks automated tools with 403 errors. Use browser-based alternatives instead.

**Setup issues**: Ensure Python is installed and run `py_venv_init.bat` to install dependencies.

## Alternative Solutions

1. **Browser Extensions**: DownloadGram, Instagram Video Downloader
2. **Online Services**: SaveFrom.net, DownloadGram.com
3. **Screen Recording**: OBS or similar software

## Notes

- For personal use only
- Respect Instagram's terms of service and content creators' rights
- Instagram actively blocks automated tools - consider this experimental
