# Instagram Downloader

A Python-based Instagram downloader that attempts to download videos and images from Instagram posts, reels, and IGTV using `instaloader`.

## ⚠️ Important Notice

**Instagram has implemented strict anti-scraping measures that may prevent this tool from working reliably.** Common issues include:

- 403 Forbidden errors when accessing Instagram's API
- Rate limiting and IP blocking
- Requirement for authentication for many posts
- Frequent changes to Instagram's internal API

## Features

- Attempts to download Instagram reels, posts, and IGTV content
- Download both videos and images from posts
- Multiple download options (videos only, images only, or both)
- Automatic clipboard detection for Instagram URLs
- Progress tracking with rich console interface
- Batch downloading support
- Comprehensive logging and error handling
- Metadata preservation options

## Supported URLs

The downloader supports Instagram URLs in the following formats:
- `https://www.instagram.com/reel/DGv6G6bzH0s/`
- `https://instagram.com/p/ABC123def/`
- `https://www.instagram.com/tv/XYZ789/`
- URLs with query parameters (e.g., `?igsh=...`)

## Setup

### 1. Initialize Virtual Environment

Run the virtual environment initialization script:

```cmd
.\py_venv_init.bat
```

This will:
- Detect available Python installations
- Create a virtual environment
- Install required dependencies from `requirements.txt`
- Upgrade packages if requested

### 2. Run the Application

After setup, run the downloader:

```cmd
.\src\main.bat
```

Or navigate to the `src` directory and run:

```cmd
cd src
.\main.bat
```

## Usage

### Interactive Mode (Recommended)

1. Run `.\src\main.bat`
2. The application will check your clipboard for Instagram URLs
3. Enter Instagram URL(s) when prompted (space-separated for multiple)
4. Choose output directory
5. Select download quality/format
6. Watch the progress and enjoy your downloads!

### Command Line Mode

You can also run the downloader with command-line arguments:

```cmd
python src\main.py -i "https://www.instagram.com/reel/DGv6G6bzH0s/" -op "C:\Downloads"
```

Arguments:
- `-i, --input_urls`: Instagram URL(s) to download
- `-op, --output_path`: Output directory path
- `--prompt`: Force interactive prompts even when arguments are provided

## Download Options

1. **Video + Images: Full Quality** - Downloads both videos and images with full metadata
2. **Video Only: Full Quality** - Downloads only videos with compressed metadata
3. **Images Only: Full Quality** - Downloads only images with compressed metadata
4. **Video + Images: Compressed** - Downloads both with minimal metadata

## Dependencies

- `instaloader` - Core Instagram downloading functionality
- `rich` - Beautiful console interface and progress bars
- `loguru` - Advanced logging
- `pyperclip` - Clipboard integration
- `requests` - HTTP requests support
- Other supporting libraries (see `requirements.txt`)

## File Structure

```
py__InstagramDownloader/
├── src/
│   ├── main.py          # Main application
│   └── main.bat         # Batch runner script
├── py_venv_init.bat     # Virtual environment setup
├── requirements.txt     # Python dependencies
└── README.md           # This file
```

## Troubleshooting

### Common Issues

1. **"No valid Python installations found"**
   - Ensure Python is installed and added to PATH
   - Try running `python --version` in command prompt

2. **"Failed to install packages"**
   - Check internet connection
   - Try running `py_venv_init.bat` again
   - Manually install with: `pip install -r requirements.txt`

3. **Download failures (Common)**
   - **403 Forbidden errors**: Instagram is blocking the requests
   - **Rate limiting**: Instagram has detected automated access
   - **Authentication required**: Some posts require login
   - **Private content**: Private posts are not accessible
   - **API changes**: Instagram frequently changes their internal API

4. **Troubleshooting Instagram restrictions**
   - Try waiting several hours before retrying
   - Use a VPN to change your IP address
   - Consider using browser-based download extensions instead
   - For reliable downloads, consider using official Instagram features

### Alternative Solutions

If this tool doesn't work due to Instagram's restrictions, consider:

1. **Browser Extensions**: Use browser extensions like "DownloadGram" or "Instagram Video Downloader"
2. **Online Services**: Use web-based services like SaveFrom.net or DownloadGram.com
3. **Screen Recording**: Use screen recording software to capture content
4. **Instagram's Built-in Features**: Use Instagram's "Save" feature for personal use

### Logs

Check `instagram_downloader.log` for detailed error information and debugging.

## Notes

- This tool is for personal use and educational purposes only
- Respect Instagram's terms of service and content creators' rights
- Instagram actively blocks automated downloading tools
- Success rates may vary and are generally low due to Instagram's restrictions
- The tool uses `instaloader`, but Instagram's anti-scraping measures often prevent it from working
- Consider this tool experimental due to Instagram's restrictions
