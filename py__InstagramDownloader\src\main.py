import argparse
import os
import re
import pyperclip
from rich.console import Console
from rich.prompt import Prompt
from rich.table import Table
from rich.progress import (
    Progress,
    SpinnerColumn,
    TextColumn,
    BarColumn,
    TaskProgressColumn,
    TimeElapsedColumn,
)
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import instaloader
from loguru import logger
import sys
from pathlib import Path
import tempfile
import shutil
from datetime import datetime

console = Console()
DOWNLOAD_TEMPLATES = {
    1: {'name': 'Video + Images: Full Quality', 'download_videos': True, 'download_images': True, 'compress_json': False},
    2: {'name': 'Video Only: Full Quality', 'download_videos': True, 'download_images': False, 'compress_json': True},
    3: {'name': 'Images Only: Full Quality', 'download_videos': False, 'download_images': True, 'compress_json': True},
    4: {'name': 'Video + Images: Compressed', 'download_videos': True, 'download_images': True, 'compress_json': True},
}

def close_logger():
    """
    Remove all logger sinks, ensuring that any open file handles (e.g. for the log file)
    are properly closed.
    """
    logger.remove()

def clean_up_logs():
    log_file = "instagram_downloader.log"
    # Close the logger to release the file handle before cleaning up
    close_logger()
    if os.path.exists(log_file):
        try:
            os.remove(log_file)
            console.print("[bold green]Successfully cleaned up log file[/bold green]")
        except Exception as e:
            console.print(f"[bold red]Error cleaning up log file: {str(e)}[/bold red]")

def parse_arguments():
    parser = argparse.ArgumentParser(description="Download content from Instagram using instaloader.")
    parser.add_argument("-i", "--input_urls", nargs="+", help="Input Instagram URL(s)")
    parser.add_argument("-op", "--output_path", type=str, help="Output directory path")
    parser.add_argument("--prompt", action="store_true", help="Prompt the user for input values")
    return parser.parse_args()

def is_valid_instagram_url(url):
    instagram_regex = re.compile(r'(https?://)?(www\.)?instagram\.com/(p|reel|tv)/([A-Za-z0-9_-]+)/?(\?.*)?')
    return instagram_regex.match(url) is not None

def extract_shortcode_from_url(url):
    """Extract the shortcode from an Instagram URL"""
    match = re.search(r'instagram\.com/(p|reel|tv)/([A-Za-z0-9_-]+)', url)
    if match:
        return match.group(2)
    return None

def prompt_inputs(args):
    # Check clipboard for an Instagram URL
    clipboard_content = pyperclip.paste()
    default_url = ""
    if is_valid_instagram_url(clipboard_content):
        default_url = clipboard_content
        console.print(f"[bold green]Instagram URL found in clipboard: {default_url}[/bold green]")

    if args.prompt or not args.input_urls:
        args.input_urls = Prompt.ask(
            "Enter the Instagram URL(s) (space-separated)", default=default_url
        ).split()
    if not args.input_urls:
        console.print("[bold red]Error: No URLs provided. Exiting...[/bold red]")
        sys.exit(1)
    if args.prompt or not args.output_path:
        args.output_path = Prompt.ask(
            "Output directory path", default=args.output_path or os.getcwd()
        )
    download_type = select_download_type()
    return args.input_urls, args.output_path, download_type

def select_download_type():
    console.print("\n[bold cyan]Select download type:[/bold cyan]")
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Option", style="dim", width=12)
    table.add_column("Description")
    for key, value in DOWNLOAD_TEMPLATES.items():
        table.add_row(str(key), value['name'])
    console.print(table)
    while True:
        try:
            choice = int(Prompt.ask("Enter your choice", default="1"))
            if choice in DOWNLOAD_TEMPLATES:
                return choice
            else:
                console.print("[bold red]Invalid choice. Please try again.[/bold red]")
        except ValueError:
            console.print("[bold red]Please enter a valid number.[/bold red]")

def wait_for_user_exit():
    console.print("\n[bold yellow]Press Enter to exit...[/bold yellow]")
    input()

class CustomLogger:
    def __init__(self, log_file="instagram_downloader.log"):
        self.logger = logger
        self.logger.remove()  # Remove any previously added sinks
        self.logger.add(sys.stderr, level="INFO", format="{time} - {level} - {message}")
        self.logger.add(log_file, level="DEBUG", format="{time} - {level} - {message} - {extra}")

    def debug(self, msg):
        self.logger.debug(msg)

    def info(self, msg):
        self.logger.info(msg)

    def warning(self, msg):
        self.logger.warning(msg)

    def error(self, msg):
        self.logger.error(msg)

    def exception(self, msg):
        self.logger.exception(msg)

def download_instagram_post(link, output_dir, download_opts, progress, task_id):
    """Download Instagram post using instaloader"""
    try:
        progress.update(task_id, description=f"[cyan]Initializing download for {link}")

        # Extract shortcode from URL
        shortcode = extract_shortcode_from_url(link)
        if not shortcode:
            raise ValueError("Could not extract shortcode from URL")

        # Create instaloader instance
        loader = instaloader.Instaloader(
            download_videos=download_opts.get('download_videos', True),
            download_video_thumbnails=False,
            download_geotags=False,
            download_comments=False,
            save_metadata=not download_opts.get('compress_json', True),
            compress_json=download_opts.get('compress_json', True),
            dirname_pattern=str(output_dir),
            filename_pattern="{date_utc:%Y-%m-%d_%H-%M-%S}_{shortcode}"
        )

        progress.update(task_id, description=f"[cyan]Fetching post data for {shortcode}")

        # Get the post
        post = instaloader.Post.from_shortcode(loader.context, shortcode)

        progress.update(task_id, description=f"[cyan]Downloading {post.typename} from @{post.owner_username}")

        # Download the post
        loader.download_post(post, target=str(output_dir))

        progress.update(task_id, completed=100, total=100)
        logger.info(f"Download successful: {link}")

        return {
            "link": link,
            "status": "success",
            "output_dir": str(output_dir),
            "post_type": post.typename,
            "username": post.owner_username,
            "caption": post.caption[:100] + "..." if post.caption and len(post.caption) > 100 else post.caption
        }

    except Exception as e:
        logger.exception(f"Error downloading {link}: {str(e)}")
        progress.update(task_id, description=f"[red]Failed: {link}")
        return {"link": link, "status": "error", "error": str(e)}

def download_instagram_posts(links, output_dir, download_opts):
    summary, errors = [], []
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        TimeElapsedColumn(),
        console=console,
    ) as progress:
        overall_task = progress.add_task("[cyan]Overall progress", total=len(links))

        # Process downloads sequentially to avoid Instagram rate limiting
        for link in links:
            task_id = progress.add_task(f"[cyan]Processing {link}", total=100)
            result = download_instagram_post(link, output_dir, download_opts, progress, task_id)

            progress.update(overall_task, advance=1)
            if result["status"] == "success":
                summary.append(result)
                post_info = f"@{result.get('username', 'unknown')} ({result.get('post_type', 'post')})"
                progress.update(
                    task_id,
                    description=f"[green]✓ Downloaded {post_info}",
                    completed=100,
                )
            else:
                errors.append(result)
                progress.update(
                    task_id,
                    description=f"[red]✗ Failed {result['link']}",
                    completed=100,
                )
    return summary, errors

def print_summary(summary, errors):
    console.print("\n" + "="*50)
    console.print("[bold green]DOWNLOAD SUMMARY[/bold green]")
    console.print("="*50)

    if summary:
        console.print(f"\n[bold green]✓ Successfully downloaded {len(summary)} post(s):[/bold green]")
        for item in summary:
            console.print(f"  • {item['link']}")
            console.print(f"    → Type: {item.get('post_type', 'Unknown')} from @{item.get('username', 'unknown')}")
            console.print(f"    → Saved to: {item['output_dir']}")
            if item.get('caption'):
                console.print(f"    → Caption: {item['caption']}")

    if errors:
        console.print(f"\n[bold red]✗ Failed to download {len(errors)} post(s):[/bold red]")
        for item in errors:
            console.print(f"  • {item['link']}")
            console.print(f"    → Error: {item['error']}")

    console.print(f"\n[bold cyan]Total processed: {len(summary) + len(errors)} post(s)[/bold cyan]")
    console.print("="*50)

def main():
    args = parse_arguments()
    links, output_path, download_type_choice = prompt_inputs(args)
    output_path = Path(output_path)
    output_path.mkdir(parents=True, exist_ok=True)

    # Get download options from template
    download_opts = DOWNLOAD_TEMPLATES[download_type_choice].copy()

    try:
        summary, errors = download_instagram_posts(links, output_path, download_opts)
        print_summary(summary, errors)

        if not errors:
            clean_up_logs()

    except Exception as e:
        console.print(f"\n[bold red]Error:[/bold red] {e}\n")
        logger.exception(f"Main execution error: {e}")
    finally:
        console.print("\n[bold green]Finished processing downloads.[/bold green]")
        wait_for_user_exit()

if __name__ == "__main__":
    main()