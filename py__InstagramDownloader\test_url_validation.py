#!/usr/bin/env python3
"""
Test script to validate Instagram URL regex pattern and shortcode extraction
"""
import re

def is_valid_instagram_url(url):
    instagram_regex = re.compile(r'(https?://)?(www\.)?instagram\.com/(p|reel|tv)/([A-Za-z0-9_-]+)/?(\?.*)?')
    return instagram_regex.match(url) is not None

def extract_shortcode_from_url(url):
    """Extract the shortcode from an Instagram URL"""
    match = re.search(r'instagram\.com/(p|reel|tv)/([A-Za-z0-9_-]+)', url)
    if match:
        return match.group(2)
    return None

# Test URLs
test_urls = [
    "https://www.instagram.com/reel/DGv6G6bzH0s/?igsh=bHNiam9wZzBlcGd0",
    "https://instagram.com/reel/DGv6G6bzH0s/",
    "https://www.instagram.com/p/ABC123def/",
    "https://www.instagram.com/tv/XYZ789/",
    "instagram.com/reel/test123",
    "www.instagram.com/reel/test456",
    "https://youtube.com/watch?v=test",  # Should fail
    "https://instagram.com/user/profile",  # Should fail
]

print("Testing Instagram URL validation and shortcode extraction:")
print("=" * 70)

for url in test_urls:
    is_valid = is_valid_instagram_url(url)
    shortcode = extract_shortcode_from_url(url) if is_valid else None
    status = "✓ VALID" if is_valid else "✗ INVALID"
    shortcode_info = f"Shortcode: {shortcode}" if shortcode else "No shortcode"
    print(f"{status:10} | {shortcode_info:20} | {url}")

print("=" * 70)
